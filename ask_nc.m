% ASK 非相干解调
clc;
clear,close all
count = 1;
err = 0;
%TRANSMITTER
fs = 100;    % sampling frequency 80MHz
fc = 70;    % carrier frequency 70MHz
% fs = 100;    % sampling frequency 100MHz
% fc = 130;    % carrier frequency 130MHz
p1 = 1;
W1 = 0.45 * fs; M = W1;  % pulse width 0.45us, M = 0.45 * 80 = 36 sample points per pluse; 0.45us is one time unit
N0 = 32; 
N1 = 1;
% N2 = 1.45 * 80 + 1;  
W2 = 1.45 * fs;
N = N0 + 14 * W2 + W1 + N0;
m = [1,1,1,0,1,0,1,0,1,0,1,1,1,1,1];
mup=zeros(1,N);
mup(N0 + N1: W2: end - N0) = m;
ps=ones(1,W1);
total = count * length(m);
phi = 2*randn;
d = 9; % SNR dB
d1 = 10^(d/10);
% sigma = 1/(2*d1);
P = (1/2)*exp(-d1/4);
while (count > 0)
    phi = 2*randn;
    x=filter(ps,1,mup); % convolve pulse shape with data
    Ts = 0.45 / M;  % sampling period 0.0125us
    figure(1), plotspec(x,Ts);
    t=1/M:1/M:length(x)/M;              % T/M-spaced time vector
    fc1 = fc / fs * M;                              % carrier frequency 70MHz
    c = cos(2 * pi * fc1 * t+phi);                   % carrier
    r=c.*x;    % modulate message with carrier    

%     r = r + sqrt(sigma) * y;
    r = awgn(r,10,'measured');
%     r = awgn(r,6,'measured');
    figure(2), plotspec(r,Ts);
    %RECEIVER
    % am demodulation of received signal sequence r

    x2 = abs(r);
    figure(3), plotspec(x2,Ts);
%     fl = 32;                               % LPF length
%     fbe = [0 0.125 0.2 1]; damps = [1 1 0 0 ]; % design of LPF parameters
    fl = 48;                               % LPF length
    fbe = [0 0.072 0.12 1]; damps = [1 1 0 0 ]; % design of LPF parameters
    b = remez(fl,fbe,damps);               % create LPF impulse response
    % x3=2*filter(b,1,x2);                 % LPF and scale downconverted signal
    x3 = pi/2 * conv(b,x2);
    figure(4), plotspec(x3,Ts);
% 生成16进制补码系数
scale_factor = 2^15 - 1; % 使用16位定点数表示
scaled_coeffs = round(b * scale_factor);

% 将系数转换为16进制补码
hex_coeffs = dec2hex(mod(scaled_coeffs, 2^16), 4);

% 将系数写入文件
fid = fopen('fir_coeffs.coe', 'w');
fprintf(fid, 'Radix = 16;\n');
fprintf(fid, 'CoefData =\n');
for i = 1:length(hex_coeffs)
    if i == length(hex_coeffs)
        fprintf(fid, '%s;\n', hex_coeffs(i,:));
    else
        fprintf(fid, '%s,\n', hex_coeffs(i,:));
    end
end
fclose(fid);

% 显示系数
disp('FIR滤波器系数 (16进制补码):');
disp(hex_coeffs);

    
    
    % 计算频率响应
    [h, w] = freqz(b, 1, 1024);

    % 绘制幅度响应
    figure(7);
    plot(w/pi, 20*log10(abs(h)));
    title('低通滤波器频率响应');
    xlabel('归一化频率 (\times\pi rad/sample)');
    ylabel('幅度 (dB)');
    grid on;
    
    % extract upsampled pulses using correlation implemented as a convolving filter
    y=filter(fliplr(ps)/(pow(ps)*M),1,x3); % filter rec'd sig with pulse; matched filter;
    % set delay to first symbol-sample and increment by M
    z=y(0.5*fl+M+N0+1:W2:end); % downsample to symbol rate
%     z=x3(0.5*fl+M/2+N0+1:W2:end);
    z = z(1:length(m));
    
    l = fl/2 + 2 * N0;
    u1 = floor((length(y) - l)/(2*W2));
    l = l - N0;
    figure(5),plot(reshape(y(l:u1*2*W2 + l -1 ), 2*W2, u1));
    figure(6),plot(reshape(x3(l:u1*2*W2 + l - 1 ), 2*W2, u1));
    plotspec(x3,Ts);
%     mprime=quantalph(z,[0,1])';
    mprime=quantabs(z,0.7)';    % quantize to +1 and 0 alphabet
    n = length(mprime);
    for i = 1: n
        if (m(i)~= mprime(i))
            err = err + 1;
        end
    end
    count = count - 1;
end

Pe = err / total

