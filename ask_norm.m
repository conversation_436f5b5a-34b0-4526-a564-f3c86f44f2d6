clc;
clear,close all
count = 1000;
err = 0;
%TRANSMITTER
fs = 80;    % sampling frequency 80MHz
p1 = 1;
W1 = 1.5 * 80; M = W1;  % pulse width 0.45us, M = 0.45 * 80 = 36 sample points per pluse; 0.8us is one time unit
N0 = 0; N1 = 0; N2= 1.5 * 80; 
N = N0 + 15 * N2 + N0;
m = [1,1,1,0,1,0,1,0,1,0,1,0,1,0,1];
mup=zeros(1,N);
mup(N0 + 1: N2: end) = m;
ps=ones(1,M);
total = count * length(m);
d = 9; % SNR dB
d1 = 10^(d/20);
sigma = 1/(2*d1);
P = (1/2)*erfc(sqrt(d1/4));
while (count > 0)
    x=filter(ps,1,mup); % convolve pulse shape with data
    Ts = 1.5 / M;  % sampling period 0.0125us
%     figure(1), plotspec(x,Ts);
    t=1/M:1/M:length(x)/M;              % T/M-spaced time vector
%     t=0:1/M:length(x)/M-1/M;
    fc = 1.5 * 70;                              % carrier frequency 70MHz
    c = cos(2 * pi * fc * t);                   % carrier
    r=c.*x;    % modulate message with carrier    
    sig = ones(1,length(r));
    sig_out =  awgn(sig,3,'measured');
    noise = sig_out - sig;
    r = r + noise;
%     r = awgn(r,3);%,'measured');
%     r = awgn(r,0,'measured');
%     figure(2), plotspec(r,Ts);
    %RECEIVER
    % am demodulation of received signal sequence r
%     fbe = [0 0.5 0.55 1]; damps = [1 1 0 0 ]; % design of LPF parameters
%     b = remez(64,fbe,damps);               % create LPF impulse response
%     r =  conv(b,r);
%     r = r(33:length(r)-32);
    
    t=1/M:1/M:1; 
    p = cos(2 * pi * fc * t);
    y=filter(fliplr(p)/(pow(p)*M),1,r);
%     figure(3), plotspec(y,Ts);
    
    fl = 64;                               % LPF length
    fbe = [0 0.25 0.3 1]; damps = [1 1 0 0 ]; % design of LPF parameters
    b = remez(fl,fbe,damps);               % create LPF impulse response
    x3=pi/2*filter(b,1,abs(y));      
%     figure(4), plotspec(x3,Ts);
    fl = 0;
%     c2 = cos(2 * pi * fc * t);                   % synchronized cosine for mixing
%     x2 = r .* c2;                            % demod received signal
%     figure(3), plotspec(x2,Ts);
%     fl = 64;                               % LPF length
%     fbe = [0 0.25 0.3 1]; damps = [1 1 0 0 ]; % design of LPF parameters
%     b = remez(fl,fbe,damps);               % create LPF impulse response
%     % x3=2*filter(b,1,x2);                 % LPF and scale downconverted signal
%     x3 = 2 * conv(b,x2);
%     figure(4), plotspec(x3,Ts);
    % extract upsampled pulses using correlation implemented as a convolving filter
%     y=filter(fliplr(ps)/(pow(ps)*M),1,x3); % filter rec'd sig with pulse; normalize
    % set delay to first symbol-sample and increment by M
    z=y(0.5*fl+M+N0:N2:end); % downsample to symbol rate
%     z=x3(0.5*64+M+N0+1:N2:end);
    l = 32;
    u1 = floor((length(y) - l-1)/(4*N2));
%     figure(5),plot(reshape(y(l:u1*4*N2 + 31 ), 4*N2, u1));
%     figure(6),plot(reshape(x3(l:u1*4*N2 + 31 ), 4*N2, u1));
    mprime=quantalph(z,[1,0])';    % quantize to +1 and 0 alphabet
    n = length(mprime);
    for i = 1: n
        if (m(i)~= mprime(i))
            err = err + 1;
        end
    end
    count = count - 1;
end

Pe = err / total





