%idsys.m:  idealized transmission system
%revised 7/8/02

clear all
clf

%TRANSMITTER
% encode text string as T-spaced PAM (+/-1, +/-3) sequence
str='01234 I wish I were an Oscar Meyer wiener 56789';
m=letters2pam(str); N=length(m);    % 4-level signal of length N
% zero pad T-spaced symbol sequence to create upsampled T/M-spaced
% sequence of scaled T-spaced pulses (with T = 1 time unit)
M=100; mup=zeros(1,N*M); mup(1:M:end)=m; % oversampling factor
% Hamming pulse filter with T/M-spaced impulse response
p=hamming(M);                       % blip pulse of width M
x=filter(p,1,mup);                  % convolve pulse shape with data
figure(1), plotspec(x,1/M)          % baseband signal spectrum
% am modulation
t=1/M:1/M:length(x)/M;              % T/M-spaced time vector
fc=20;                              % carrier frequency
c=cos(2*pi*fc*t);                   % carrier
r=c.*x;                             % modulate message with carrier

%************************* time-varying fading channel***********
% ds=pow(r);                      % desired average power of signal
% lr=length(r);                       % length of transmitted signal vector
% fp=[ones(1,floor(0.2*lr)),0.5*ones(1,lr-floor(0.2*lr))]; %flat fading profile
% r=r.*fp;                            % apply profile to transmitted signal vector
% ds=pow(r);
%*********************************************************%
%*******************fading plus automatic gain control***********%
% g=zeros(1,lr); g(1)=1;              % initialize AGC parameter
% nr=zeros(1,lr);              
% mu=.0003;                           % algorithm stepsize
% for i=1:lr-1
%     nr(i)=g(i)*r(i);                % normalize by g(i) to get nr[i]
%     g(i+1)=g(i)-mu*(nr(i)^2-ds);     % adaptive update of a(k)
% end
% r=nr;                               % received signal is still called r

%RECEIVER
% am demodulation of received signal sequence r
c2=cos(2*pi*fc*t);                   % synchronized cosine for mixing
x2=r.*c2;                            % demod received signal
fl=50;                               % LPF length
fbe=[0 0.5 0.6 1]; damps=[1 1 0 0 ]; % design of LPF parameters
b=remez(fl,fbe,damps);               % create LPF impulse response
% x3=2*filter(b,1,x2);                 % LPF and scale downconverted signal
x3=2*conv(b,x2);
% extract upsampled pulses using correlation implemented as a convolving filter
y=filter(fliplr(p)/(pow(p)*M),1,x3); % filter rec'd sig with pulse; normalize
% set delay to first symbol-sample and increment by M
z=y(0.5*fl+M:M:end);                 % downsample to symbol rate
% z=y(M:M:end); 
figure(2), plot([1:length(z)],z,'.') % soft decisions
% decision device and symbol matching performance assessment
mprime=quantalph(z,[-3,-1,1,3])';    % quantize to +/-1 and +/-3 alphabet
cluster_variance=(mprime-z)*(mprime-z)'/length(mprime), % cluster variance
lmp=length(mprime);
percentage_symbol_errors=100*sum(abs(sign(mprime-m(1:lmp))))/lmp, % symb err
% decode decision device output to text string
reconstructed_message=pam2letters(mprime)      % reconstruct message


