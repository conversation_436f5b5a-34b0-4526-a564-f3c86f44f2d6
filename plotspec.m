% plotspec(x,Ts) plots the spectrum of the signal x
% Ts = time (in seconds) between adjacent samples in x

function c=plotspec(x,Ts)

N=length(x);                               % length of the signal x
t=Ts*(1:N);                                % define a time vector 
cc=t;
ssf=(-N/2:N/2-1)/(Ts*N);                   % frequency vector
fx=fft(x(1:N));                            % do DFT/FFT
fxs=fftshift(fx);                          % shift it for plotting
subplot(2,1,1), plot(t,x)                  % plot the waveform
ylim([-2,2]);
% ylim([-4,4]);
xlabel('time (us)'); ylabel('amplitude')     % label the axes
subplot(2,1,2), plot(ssf,abs(fxs))         % plot magnitude spectrum
xlabel('frequency (MHz)'); ylabel('magnitude')   % label the axes
%ylim([0,350]);
c=cc;

%verify parseval equalize power using
%sum(abs(fxs).^2)/N
%sum(abs(x).^2)
% use axis([0,2,-1.1,1.1]) for specsquare.eps
