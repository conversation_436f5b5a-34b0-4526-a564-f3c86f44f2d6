function y=quantabs(x,th)
% function  y=quantalph(x,alphabet)
% quantize the input signal x to the alphabet 
% using nearest neighbor method
% input x - vector to be quantized
%       alphabet - vector of discrete values that y can take on 
%                  sorted in ascending order
% output y - quantized vector
% [r c] = size(alphabet); if c>r, alphabet=alphabet'; end
[r c] = size(x); if c>r, x=x'; end
dist=(x-th);
y = zeros(length(x),1);
for i = 1: length(x)
    if dist(i) >= 0
        y(i) = 1;
    else
        y(i) = 0;
    end
end

