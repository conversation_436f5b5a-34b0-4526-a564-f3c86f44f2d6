% speccos.m plot the spectrum of a cosine wave
clc;
clear,close all;
f=10; phi=0;                % specify frequency and phase
% phi = 1*pi;
time=2;                     % length of time 
Ts=1/100;  % time interval between samples
t=Ts:Ts:time;               % create a time vector
x=cos(2*pi*f*t+phi);        % create cos wave
m=length(t);
n=1;
for n=1:m
% x(n)=cos(2*pi*f*t(n)+10*pi*t(n));
% x(n)=cos(2*pi*f*t(n)+20*pi*t(n)*t(n));
% x(n)=cos(2*pi*sin(2*pi*t(n)*t(n))+phi);
% x(n)=cos(2*pi*sin(2*pi*t(n))+phi);
% x(n)=cos(2*pi*t(n)*t(n)+phi);
n=n+1;
end
plotspec(x,Ts);              % draw waveform and spectrum
