% specsquare.m plot the spectrum of a square wave
clear close all;
clc;
f=10;                       % "frequency" of square wave
time=2;                     % length of time 
Ts=1/1000;                  % time interval between samples
t=Ts:Ts:time;               % create a time vector
aa=t;
x=sign(cos(2*pi*f*t));      % square wave = sign of cos wave
%x=(sin(2*pi*f*t));
bb=plotspec(x,Ts);              % call plotspec to draw spectrum
